import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useContext } from "react";
import RegistrationContext from "../../../../context/Registration/RegistrationContext";
const Header = () => {
  const router = useRouter();
  const { setIsModalOpen } = useContext(RegistrationContext);


  useEffect(() => {
    const hamburger = {
      navToggle: document.querySelector(".nav-trigger"),
      navBody: document.querySelector("body"),
      doToggle: function (e) {
        e.preventDefault();
        this.navBody.classList.toggle("open");
      },
    };
    hamburger.navToggle.addEventListener("click", (e) => {
      hamburger.doToggle(e);
    });
  }, []);

  useEffect(() => {
    document.querySelector("body").classList.remove("open");
  }, [router.pathname])

  const handleClick = (e) => {
    const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"])
    modalOverlay[0].classList.add("ReactModal__FadeIn");
    e.preventDefault();
    setIsModalOpen(true);
  }

  return (
    <>
      <header className="header fluid">
        <div className="container">
          <div className="flex">
            <div className="logo">
              <Link href="/">
                <img src="/assets/images/logo-urw.svg" alt="urw logo" title="URW" width="184" />
              </Link>
            </div>
            <a href="#" className="nav-trigger">
              <i className="icon-hamburger"></i>
              <i className="icon-close"></i>
            </a>
            <div className="menu-overlay">
              <div className="menu-wrapper">
                <ul className="menu">
                  <li>
                    <Link href="/about/" className={router.pathname.indexOf("/about") != -1 ? "active" : ""}>
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link href="/portfolio/" className={router.pathname.indexOf("/portfolio") != -1 ? "active" : ""}>
                      Portfolio
                    </Link>
                  </li>
                  <li>
                    <Link href="/leasing/" className={router.pathname.indexOf("/leasing") != -1 ? "active" : ""}>
                      Leasing
                    </Link>
                  </li>
                  <li>
                    <Link href="/community/" className={router.pathname.indexOf("/community") != -1 ? "active" : ""}>
                      Community
                    </Link>
                  </li>
                  <li>
                    <Link href="/sustainability/" className={router.pathname.indexOf("/sustainability") != -1 ? "active" : ""}>
                      Sustainability
                    </Link>
                  </li>

                  <li>
                    <Link href="/news/" className={router.pathname.indexOf("/news") != -1 ? "active" : ""}>
                      News
                    </Link>
                  </li>
                </ul>
                <Link href="/supplier-diversity" className="btn btn-outline-black">Supplier Diversity</Link>
                <Link href="/connect/" className="btn btn-secondary">CONTACT US</Link>
              </div>
            </div>
            <div className="header-btn">
              <Link href="/supplier-diversity" className="btn btn-outline-black">Supplier Diversity</Link>
              <Link href="/connect" className="btn btn-secondary">CONTACT US</Link>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
