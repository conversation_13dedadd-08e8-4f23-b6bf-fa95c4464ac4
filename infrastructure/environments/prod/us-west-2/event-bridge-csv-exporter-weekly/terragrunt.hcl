## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix      = local.envvars.locals.resource_prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Dependencies
dependency "lambda_csv_exporter" {
  config_path                             = "../lambda-csv-exporter"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Terraform config
terraform {
  source = "../../../..//modules/event-bridge"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  name                = "${local.project}-${local.env}-schedule-trigger-csv-exporter-lambda-weekly"
  is_enabled          = true
  schedule_expression = "cron(0 0 ? * MON *)"
  lambda_arn          = dependency.lambda_csv_exporter.outputs.arn
  input               = jsonencode({
    schedule = "weekly"
    forms    = ["registerNow"]
  })
}
